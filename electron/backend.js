const path = require('path')
const fs = require('fs')
const { spawn, ChildProcess } = require('child_process')

const SERVER_CONFIG = {
  PORT: 9000,
  HOST: 'localhost',
  TIMEOUT: 600000, // 增加到60秒，给应用更多启动时间
  MAX_RETRIES: 3,
  RETRY_INTERVAL: 2000
}

// 路径配置工具函数
const getAppPath = (env, ...segments) => {
  const basePath = env === 'development' ? __dirname : process.resourcesPath
  return path.join(basePath, ...segments)
}
// 检查端口是否被占用
async function checkPortInUse(port) {
  return new Promise(resolve => {
    const { exec } = require('child_process')
    exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
      if (error || !stdout.trim()) {
        resolve(null) // 端口未被占用
      } else {
        // 解析进程ID
        const lines = stdout.trim().split('\n')
        const pids = lines
          .map(line => {
            const parts = line.trim().split(/\s+/)
            return parts[parts.length - 1]
          })
          .filter(pid => pid && pid !== '0')
        resolve(pids.length > 0 ? pids[0] : null)
      }
    })
  })
}

// 强制结束占用端口的进程
async function killProcessByPid(pid) {
  return new Promise(resolve => {
    const { exec } = require('child_process')
    exec(`taskkill /f /pid ${pid}`, error => {
      if (error) {
        console.log(`结束进程失败: ${error.message}`)
      }
      resolve()
    })
  })
}

// 清理端口占用
async function clearPort(port) {
  const pid = await checkPortInUse(port)
  if (pid) {
    await killProcessByPid(pid)
    // 等待一秒确保进程完全结束
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}

function checkServer(port = SERVER_CONFIG.PORT, host = SERVER_CONFIG.HOST, timeout = SERVER_CONFIG.TIMEOUT, maxRetries = SERVER_CONFIG.MAX_RETRIES, javaExecutable, jarPath) {
  let isConnected = false
  let javaProcess = null
  return new Promise(async (resolve, reject) => {
    // 检查Java可执行文件是否存在
    // if (!fs.existsSync(javaExecutable)) {
    //     console.error(`Java可执行文件不存在: ${javaExecutable}`)
    //     reject(new Error(`Java可执行文件不存在: ${javaExecutable}`))
    //     return
    // }

    // 检查JAR文件是否存在
    if (!fs.existsSync(jarPath)) {
      console.error(`JAR文件不存在: ${jarPath}`)
      reject(new Error(`JAR文件不存在: ${jarPath}`))
      return
    }

    try {
      // 清理端口占用
      await clearPort(port)
    } catch (error) {
      console.error('清理端口失败:', error)
    }

    const startJavaProcess = () => {
      const args = ['-Dfile.encoding=UTF-8', '-jar', jarPath, `--server.port=${port}`]
      javaProcess = spawn(javaExecutable, args)

      let startupPhase = '初始化'

      javaProcess.stdout.on('data', data => {
        if (!isConnected) {
          const output = data.toString()
          console.log('Java输出:', output)

          // 跟踪启动阶段
          if (output.includes('Creating shared instance')) {
            startupPhase = '创建Bean实例'
          } else if (output.includes('Tomcat initialized')) {
            startupPhase = 'Tomcat初始化'
          } else if (output.includes('Tomcat started')) {
            startupPhase = 'Tomcat启动完成'
            resolve(javaProcess)
          }

          console.log(`当前启动阶段: ${startupPhase}`)
        }
      })

      javaProcess.stderr.on('data', data => {
        const errorOutput = data.toString()
        console.error(`Java进程错误: ${errorOutput}`)
        // 检查是否是致命错误
        if (errorOutput.includes('Exception') || errorOutput.includes('Error')) {
          console.error('检测到Java应用错误，可能影响启动')
        }
      })

      javaProcess.on('error', error => {
        console.error(`Java进程启动错误:`, error)
        if (!isConnected) {
          reject(error)
        }
      })

      javaProcess.on('close', code => {
        console.log(`Java进程已关闭，退出码: ${code}`)
        if (code !== 0 && !isConnected) {
          reject(new Error(`Java进程异常退出，代码: ${code}`))
        }
      })
    }

    // 添加超时处理
    const timeoutId = setTimeout(() => {
      if (!isConnected) {
        console.error('Java服务启动超时')
        reject(new Error('Java服务启动超时'))
      }
    }, 60000000)

    // 如果成功连接，清除超时
    const originalResolve = resolve
    resolve = value => {
      isConnected = true
      // clearTimeout(timeoutId)
      originalResolve(value)
    }

    const originalReject = reject
    reject = error => {
      isConnected = true
      // clearTimeout(timeoutId)
      originalReject(error)
    }

    startJavaProcess()
  })
}

// 清理 Java 进程
function cleanupJavaProcess(javaProcess) {
  return new Promise(resolve => {
    if (!javaProcess || (!javaProcess) instanceof ChildProcess) {
      resolve()
      return
    }

    console.log('🛑 正在关闭 Java 服务...')
    isShuttingDown = true

    // 设置超时强制结束
    const forceKillTimeout = setTimeout(() => {
      if (javaProcess && !javaProcess.killed) {
        console.log('⚡ 强制结束 Java 进程...')
        javaProcess.kill('SIGKILL')
      }
      resolve()
    }, 5000)

    // 尝试优雅关闭
    javaProcess.on('close', () => {
      clearTimeout(forceKillTimeout)
      console.log('✅ Java 服务已关闭')
      javaProcess = null
      resolve()
    })

    // 发送关闭信号
    if (process.platform === 'win32') {
      spawn('taskkill', ['/pid', javaProcess.pid, '/f', '/t'])
    } else {
      javaProcess.kill('SIGTERM')
    }
  })
}
// 获取Java相关路径
const getJavaPath = () => {
  // 更可靠的环境检测：检查是否在app.asar中或者__dirname包含resources
  const isProduction = __dirname.includes('resources') || __dirname.includes('app.asar') || process.env.NODE_ENV === 'production'
  const env = isProduction ? 'production' : 'development'

  console.log('环境检测:', {
    __dirname,
    isProduction,
    env,
    'process.env.NODE_ENV': process.env.NODE_ENV
  })

  const jarPath = getAppPath(env, 'java', 'java.jar')
  let javaExecutable

  if (env === 'development') {
    // 开发环境使用系统Java
    javaExecutable = 'java'
  } else {
    // 生产环境：首先尝试使用打包的JRE，如果不存在则使用系统Java
    const packagedJava = getAppPath(env, 'java', 'jre', 'bin', process.platform === 'win32' ? 'java.exe' : 'java')
    if (fs.existsSync(packagedJava)) {
      javaExecutable = packagedJava
    } else {
      console.log('打包的JRE不存在，使用系统Java')
      javaExecutable = 'java'
    }
  }
  return { jarPath, javaExecutable, env }
}

module.exports = {
  checkPortInUse,
  killProcessByPid,
  clearPort,
  checkServer,
  cleanupJavaProcess,
  getJavaPath
}
