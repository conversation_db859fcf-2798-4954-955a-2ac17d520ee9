<template>
  <div>
    <!-- 页面头部 -->
    <el-card class="mb-6" shadow="never">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex-1">
          <div class="flex items-center gap-4 mb-3">
            <el-button @click="goBack" circle class="!w-10 !h-10 !bg-gray-100 hover:!bg-blue-50 !border-none">
              <el-icon class="text-gray-600 hover:text-blue-600 transition-colors duration-200" size="20">
                <ArrowLeft />
              </el-icon>
            </el-button>
            <h1 class="text-3xl font-bold text-gray-900">
              {{ shipInfo.shipName || '船舶详情' }}
            </h1>
            <el-tag :type="getShipTypeTagType(shipInfo.shipType)" size="large" round>
              {{ shipInfo.shipType || '未知类型' }}
            </el-tag>
          </div>
          <div class="flex items-center gap-6 text-gray-600">
            <div class="flex items-center gap-2">
              <el-icon size="16">
                <User />
              </el-icon>
              <span>船东：{{ shipInfo.shipOwner || '未知' }}</span>
            </div>
            <div class="flex items-center gap-2">
              <el-icon size="16">
                <Location />
              </el-icon>
              <span>航区：{{ shipInfo.area || '未知' }}</span>
            </div>
            <div class="flex items-center gap-2">
              <el-icon size="16">
                <Ship />
              </el-icon>
              <span>船厂：{{ shipInfo.shipYard || '未知' }}</span>
            </div>
            <div class="flex items-center gap-2">
              <el-icon size="16">
                <Document />
              </el-icon>
              <span>imo号：{{ shipInfo.imo || '未知' }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 船舶基本信息卡片 -->
    <el-card class="mb-6" shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex items-center gap-2">
            <el-icon class="text-blue-600" size="20">
              <DataBoard />
            </el-icon>
            <span class="text-xl font-semibold text-gray-900">基本参数</span>
          </div>
          <el-button @click="toggleBasicParams" circle class="!w-8 !h-8 hover:!bg-gray-100 !border-none">
            <el-icon class="text-gray-600 hover:text-gray-800 transition-all duration-200" :class="{ 'rotate-180': !isBasicParamsCollapsed }" size="16">
              <ArrowDown />
            </el-icon>
          </el-button>
        </div>
      </template>

      <el-collapse-transition>
        <div v-show="!isBasicParamsCollapsed">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-6">
            <custom-tag label="总长(LOA)" :value="shipInfo.shipLoa || 0" unit="m" type="success" />
            <custom-tag label="型宽" :value="shipInfo.moldWidth || 0" unit="m" type="warning" />
            <custom-tag label="型深" :value="shipInfo.moldDepth || 0" unit="m" type="info" />
            <custom-tag label="结构吃水" :value="shipInfo.structDraft || 0" unit="m" type="info" />
            <custom-tag label="设计吃水" :value="shipInfo.designDraft || 0" unit="m" type="success" />
            <custom-tag label="垂线间长(LPP)" :value="shipInfo.shipLpp || 0" unit="m" type="info" />
            <custom-tag label="规范船长" :value="shipInfo.shipLrule || 0" unit="m" type="success" />
            <custom-tag label="梁拱" :value="shipInfo.beamArch || 0" unit="m" type="info" />
            <custom-tag label="排水量" :value="shipInfo.dwt || 0" unit="t" type="success" />
            <custom-tag label="空船重量" :value="shipInfo.lsw || 0" unit="t" type="warning" />
            <custom-tag label="CSA" :value="shipInfo.csa || 0" type="success" />
            <custom-tag label="CSM" :value="shipInfo.csm || 0" type="warning" />
            <custom-tag label="铺龙骨日期" :value="shipInfo.keelDate || 0" type="success" />
            <custom-tag label="航行工况静水弯矩-中拱" :value="shipInfo.dySwbmHogg || 0" unit="kNm" type="success" />
            <custom-tag label="航行工况静水弯矩-中垂" :value="shipInfo.dySwbmSagg || 0" unit="kNm" type="info" />
            <custom-tag label="港内工况静水弯矩-中拱" :value="shipInfo.staSwbmHogg || 0" unit="kNm" type="warning" />
            <custom-tag label="港内工况静水弯矩-中垂" :value="shipInfo.staSwbmSagg || 0" unit="kNm" type="info" />
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-1 gap-6">
            <div class="p-4 rounded-xl border border-gray-200 bg-gradient-to-br from-blue-50 to-blue-100">
              <div class="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                <el-icon class="text-green-600" size="16">
                  <SuccessFilled />
                </el-icon>
                功能附加标志
              </div>
              <div class="flex flex-wrap gap-2">
                <el-tag v-for="(value, key) in shipInfo.features" :key="key" type="success" size="default" round>{{ key }}: {{ value }}</el-tag>
                <div v-if="!shipInfo.features || Object.keys(shipInfo.features).length === 0" class="text-gray-500 text-sm">暂无标志信息</div>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </el-card>

    <!-- 详细信息标签页 -->
    <el-card shadow="never">
      <el-tabs v-model="activeTab" class="custom-tabs" @tab-change="handleTabChange">
        <el-tab-pane name="信息概览">
          <template #label>
            <div class="flex items-center gap-2">
              <el-icon size="16">
                <Grid />
              </el-icon>
              <span>信息概览</span>
            </div>
          </template>
          <div v-if="shipSumary.report.length" class="min-h-[500px]">
            <div v-for="drawing in shipSumary.report" :key="drawing.id">
              <PdfCanvas :maxPage="1" class="w-full" :url="drawing.filePath" />
              <h4 class="font-semibold text-gray-900 m-2 text-center">{{ drawing.subTitle }}</h4>
            </div>
          </div>
          <el-empty description="暂无船舶结构数据" v-else class="min-h-[500px]" />
        </el-tab-pane>
        <el-tab-pane name="船舶结构">
          <template #label>
            <div class="flex items-center gap-2">
              <el-icon size="16">
                <Grid />
              </el-icon>
              <span>船舶结构</span>
            </div>
          </template>
          <div v-if="shipStruct.filePath" class="min-h-[500px]">
            <div>
              <PdfCanvas :maxPage="1" class="w-full" :url="shipStruct.filePath" />
              <h4 class="font-semibold text-gray-900 m-2 text-center">{{ shipStruct.subTitle }}</h4>
            </div>
          </div>
          <el-empty description="暂无船舶结构数据" v-else class="min-h-[500px]" />
        </el-tab-pane>
        <el-tab-pane name="计算模型">
          <template #label>
            <div class="flex items-center gap-2">
              <el-icon size="16">
                <Document />
              </el-icon>
              <span>计算模型</span>
            </div>
          </template>
          <div v-if="calcModel.filePath" class="min-h-[500px]">
            <div>
              <PdfCanvas :maxPage="1" class="w-full" :url="calcModel.filePath" />
              <h4 class="font-semibold text-gray-900 m-2 text-center">{{ shipStruct.subTitle }}</h4>
            </div>
          </div>
          <el-empty description="暂无计算模型数据" v-else class="min-h-[500px]" />
        </el-tab-pane>
        <el-tab-pane name="计算报告">
          <template #label>
            <div class="flex items-center gap-2">
              <el-icon size="16">
                <TrendCharts />
              </el-icon>
              <span>计算报告</span>
            </div>
          </template>
          <div v-if="calcReport.filePath" class="min-h-[500px]">
            <div>
              <PdfCanvas :maxPage="1" class="w-full" :url="calcReport.filePath" />
              <h4 class="font-semibold text-gray-900 m-2 text-center">{{ shipStruct.subTitle }}</h4>
            </div>
          </div>
          <el-empty description="暂无计算报告数据" v-else class="min-h-[500px]" />
        </el-tab-pane>

        <el-tab-pane name="装载信息">
          <template #label>
            <div class="flex items-center gap-2">
              <el-icon size="16">
                <Box />
              </el-icon>
              <span>装载信息</span>
            </div>
          </template>
          <div v-if="loadInfo.filePath" class="min-h-[500px]">
            <div>
              <PdfCanvas :maxPage="1" class="w-full" :url="loadInfo.filePath" />
              <h4 class="font-semibold text-gray-900 m-2 text-center">{{ shipStruct.subTitle }}</h4>
            </div>
          </div>
          <el-empty description="暂无装载信息数据" v-else class="min-h-[500px]" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElCard, ElTable, ElTableColumn, ElTabs, ElTabPane, ElTag, ElImage, ElProgress, ElCollapseTransition, ElDialog, ElForm, ElFormItem, ElUpload } from 'element-plus'
import { ArrowLeft, User, Location, DataBoard, ArrowDown, SuccessFilled, OfficeBuilding, Grid, Document, TrendCharts, Box, Coordinate, DataAnalysis, Memo } from '@element-plus/icons-vue'
import Apis from '@/apis'
import CustomTag from '@/components/CustomTag/CustomTag.vue'
import PdfCanvas from '@/components/PdfCanvas/index.vue'
import useAppStore from '@/store/modules/app'
const path = window.electronAPI.path
const appStore = useAppStore()
// const path = require('path')
const route = useRoute()
const router = useRouter()
const activeTab = ref('信息概览')
// 折叠状态控制
const isBasicParamsCollapsed = ref(false)
// 根据API数据结构调整shipInfo
const shipInfo = ref({
  id: null,
  shipName: '', // 船舶名称
  shipOwner: '', // 船主
  shipType: '', // 船舶类型
  area: '', // 航区
  shipYard: '', // 船厂
  imo: '', // IMO号
  shipLoa: 0, // 总长（LOA) m
  shipLpp: 0, // 垂线间长（LPP) m
  shipLrule: 0, // 规范船长 m
  beamArch: 0, // 梁拱 m
  moldDepth: 0, // 型深 m
  moldWidth: 0, // 型宽 m
  structDraft: 0, // 结构吃水 m
  designDraft: 0, // 设计吃水 m
  dySwbmHogg: 0, // 航行工况静水弯矩-中拱 kNm
  dySwbmSagg: 0, // 航行工况静水弯矩-中垂 kNm
  staSwbmHogg: 0, // 港内工况静水弯矩-中拱 kNm
  staSwbmSagg: 0, // 港内工况静水弯矩-中垂 kNm
  dwt: 0, // 排水量 t
  lsw: 0, // 空船重量 t
  ndw: 0, // 净载重量 t
  csa: '', // 入级符号-结构和设备
  csm: '', // 入级符号-机械轮机和电气
  keelDate: '', // 铺龙骨日期
  features: {} // 功能特征
})

// 模拟数据已移除，改为通过API获取
const goBack = () => {
  console.log('back')
  router.push({ name: 'shipList' })
}

// 切换基本参数折叠状态
const toggleBasicParams = () => {
  isBasicParamsCollapsed.value = !isBasicParamsCollapsed.value
}

// 样式方法
const getShipTypeTagType = type => {
  const typeMap = {
    油船: 'success',
    散货船: 'warning',
    集装箱船: 'info',
    客船: 'primary',
    挖泥船: 'danger'
  }
  return typeMap[type] || 'info'
}

const loadShipDetail = async () => {
  const shipId = parseInt(route.params.id)
  if (isNaN(shipId)) {
    ElMessage.error('无效的船舶ID')
    router.push({ name: 'shipList' })
    return
  }

  try {
    const response = await Apis.general.get_shipstruct_ship_struct_getshipdetail({
      params: {
        id: shipId
      }
    })

    if (response.code === 200) {
      shipInfo.value = response.data
      ElMessage.success('船舶详情加载成功')
    } else {
      ElMessage.error(response.msg || '获取船舶详情失败')
    }
  } catch (error) {
    console.error('获取船舶详情失败:', error)
    ElMessage.error('获取船舶详情失败')
    router.push({ name: 'shipList' })
  }
}
const shipSumary = ref({ report: [], allowStress: [] })
const loadShipSumary = async () => {
  const response = await Apis.general.get_shipstruct_ship_getsummary({
    params: {
      shipId: route.params.id
    }
  })
  if (response && response.code === 200) {
    shipSumary.value = response.data
    // shipSumary.value.report = [
    //   { filePath: path.resolve('D:/项目资料/船舶与海洋工程结构数据分析及支持服务平台/基本结构图.pdf'), subTitle: '基本结构图' },
    //   { filePath: path.resolve('D:/项目资料/船舶与海洋工程结构数据分析及支持服务平台/典型横剖面图.pdf'), subTitle: '典型横剖面图' }
    // ]
  } else {
    ElMessage.error(response.msg || '获取船舶详情失败')
  }
}
const shipStruct = ref({ filePath: '', subTitle: '' })
const calcModel = ref({ filePath: '', subTitle: '' })
const calcReport = ref({ filePath: '', subTitle: '' })
const loadInfo = ref({ filePath: '', subTitle: '' })
const loadReportData = async type => {
  let data
  switch (type) {
    case '船舶结构':
      data = shipStruct
      break
    case '计算模型':
      data = calcModel
      break
    case '计算报告':
      data = calcReport
      break
    case '装载信息':
      data = loadInfo
  }
  const res = await Apis.general.post_shipstruct_ship_getreports({
    data: {
      shipId: route.params.id,
      reportType: type,
      subTitle: ''
    }
  })
  if (res && res.code === 200) {
    data.value = res.data
    data.value = { filePath: path.resolve('D:/项目资料/船舶与海洋工程结构数据分析及支持服务平台/基本结构图.pdf'), subTitle: '基本结构图' }
  }
}
const handleTabChange = tab => {
  if (tab === '信息概览') {
    loadShipSumary()
  } else {
    loadReportData(tab)
  }
}
onMounted(() => {
  loadShipDetail()
  loadShipSumary()
  nextTick(() => {
    appStore.setSidebarCollapse(true)
  })
})
</script>

<style lang="scss" scoped></style>
